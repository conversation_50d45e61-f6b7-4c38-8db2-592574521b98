<template>
  <div class="vpp-info-page">
    <div class="vpp-info-container">
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <h2 class="page-title">{{ $T("虚拟电厂厂站信息") }}</h2>
        <el-button type="primary" @click="handleConfigVpp">
          {{ $T("配置虚拟电厂") }}
        </el-button>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 虚拟电厂基本信息卡片 -->
        <div class="vpp-info-section">
          <!-- 左侧：图片回显 -->
          <div class="vpp-image-container">
            <img
              :src="vppInfo.image || defaultImage"
              alt="虚拟电厂"
              class="vpp-image"
            />
          </div>

          <!-- 右侧：基本信息 -->
          <div class="vpp-basic-info">
            <!-- 第一行 -->
            <el-row :gutter="24">
              <el-col :span="8">
                {{ $T("虚拟电厂名称：") }}{{ vppInfo.vppName || "--" }}
              </el-col>
              <el-col :span="8">
                {{ $T("虚拟电厂所属省份：") }}{{ vppInfo.province || "--" }}
              </el-col>
              <el-col :span="8">
                {{ $T("虚拟电厂站类型：") }}{{ vppInfo.stationType || "--" }}
              </el-col>
            </el-row>
            <!-- 第二行 -->
            <el-row :gutter="24">
              <el-col :span="8">
                {{ $T("虚拟电厂成立时间：")
                }}{{ vppInfo.establishDate || "--" }}
              </el-col>
              <el-col :span="8">
                {{ $T("运营商编号：") }}{{ vppInfo.operatorCode || "--" }}
              </el-col>
            </el-row>
            <!-- 第三行 -->
            <el-row :gutter="24">
              <el-col :span="8">
                {{ $T("虚拟电厂厂站类型说明：")
                }}{{ vppInfo.stationTypeDesc || "--" }}
              </el-col>
            </el-row>

            <!-- 技术参数卡片区域 -->
            <div>
              <el-row :gutter="16">
                <!-- 需求响应卡片 -->
                <el-col :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("需求响应") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.demandResponse.priceUpper || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.demandResponse.priceLower || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>

                <!-- 调峰卡片 -->
                <el-col :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("调峰") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.peakRegulation.priceUpper || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.peakRegulation.priceLower || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>

                <!-- 调频卡片 -->
                <el-col :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("调频") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{
                            techParams.frequencyRegulation.priceUpper || "--"
                          }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{
                            techParams.frequencyRegulation.priceLower || "--"
                          }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>

              <!-- 第二行：调节和能量卡片 -->
              <el-row :gutter="16" style="margin-top: 16px">
                <!-- 调节卡片 - 仅当类型包含调节型时显示 -->
                <el-col v-if="isRegulationType" :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("调节") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.regulation.priceUpper || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.regulation.priceLower || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>

                <!-- 能量卡片 - 仅当类型包含能量型时显示 -->
                <el-col v-if="isEnergyType" :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("能量") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.energy.priceUpper || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ techParams.energy.priceLower || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 虚拟电厂资源统计 -->
        <div class="stats-section">
          <div class="stats-header">
            <h3 class="stats-title">{{ $T("虚拟电厂资源统计") }}</h3>
          </div>
          <div class="stats-cards">
            <div class="stats-card">
              <div class="stats-icon">
                <img
                  src="static/image/enterprise_user_count.png"
                  alt="用户图标"
                  class="icon-image"
                />
              </div>
              <div class="stats-info">
                <div class="stats-label">
                  {{ $T("接入企业/用户数量（家）") }}
                </div>
                <div class="stats-value">
                  {{ capacityStats.userCount || "--" }}
                </div>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon">
                <img
                  src="static/image/resource_count.png"
                  alt="资源图标"
                  class="icon-image"
                />
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ $T("聚合资源数量（个）") }}</div>
                <div class="stats-value">
                  {{ capacityStats.resourceCount || "--" }}
                </div>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon">
                <img
                  src="static/image/capacity.png"
                  alt="容量图标"
                  class="icon-image"
                />
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ $T("可调节容量（MW）") }}</div>
                <div class="stats-value">
                  {{ capacityStats.totalCapacity || "--" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ConfigDialog
        :visible.sync="configDialogVisible"
        :vpp-data="vppInfo"
        :province-options="provinceOptions"
        @confirm="handleConfigConfirm"
      />
    </div>
  </div>
</template>

<script>
import ConfigDialog from "./components/ConfigDialog.vue";
import { getVppById, updateVpp } from "@/api/vpp-config";
import { getGeographicalData } from "@/api/base-config";

export default {
  components: {
    ConfigDialog
  },
  name: "VirtualPowerPlantInfo",
  data() {
    return {
      // 默认电厂图片 - 使用占位符
      defaultImage:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04MCA2MEg0MFY5MEg4MFY2MFoiIGZpbGw9IiNEREREREQiLz4KPHA+dGggZD0iTTE2MCA2MEgxMjBWOTBIMTYwVjYwWiIgZmlsbD0iI0RERERERCIvPgo8cGF0aCBkPSJNMTAwIDMwSDEwMFY2MEgxMDBWMzBaIiBzdHJva2U9IiNEREREREQiIHN0cm9rZS13aWR0aD0iMiIvPgo8dGV4dCB4PSIxMDAiIHk9IjEyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OTk5OSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIj7ooZXmi6XnlLXljoI8L3RleHQ+Cjwvc3ZnPgo=",
      // 虚拟电厂基本信息
      vppInfo: {
        name: "",
        province: "",
        stationType: "",
        establishTime: "",
        operatorCode: "",
        stationTypeDesc: "",
        image: "",
        // 虚拟电厂类型数组，支持多选
        // 1=发电类, 2=负荷类, 3=调节型, 4=能量型
        vppTypes: []
      },
      // 技术参数
      techParams: {
        demandResponse: {
          priceUpper: "",
          priceLower: ""
        },
        peakRegulation: {
          priceUpper: "",
          priceLower: ""
        },
        frequencyRegulation: {
          priceUpper: "",
          priceLower: ""
        },
        regulation: {
          priceUpper: "",
          priceLower: ""
        },
        energy: {
          priceUpper: "",
          priceLower: ""
        }
      },
      // 容量统计数据
      capacityStats: {
        userCount: "",
        resourceCount: "",
        totalCapacity: ""
      },
      configDialogVisible: false,
      provinceOptions: []
    };
  },
  computed: {
    // 是否包含调节型
    isRegulationType() {
      return this.vppInfo.vppTypes.includes(3);
    },
    // 是否包含能量型
    isEnergyType() {
      return this.vppInfo.vppTypes.includes(4);
    }
  },

  methods: {
    // 加载虚拟电厂数据
    async loadVppData() {
      try {
        // 调用API获取ID为1的虚拟电厂数据
        const response = await getVppById(1);
        if (response.code === 0 && response.data) {
          const data = response.data;

          // 映射API返回数据到页面数据结构
          this.vppInfo = {
            vppName: data.vppName || "",
            province: this.getProvinceNameByCode(data.province) || "",
            stationType: this.getVppTypeText(data.vppType) || "",
            establishDate: data.createTime || "",
            operatorCode: data.operatorcode || "",
            image: data.picture || "",
            vppType: data.vppType || ""
          };

          // 映射技术参数
          this.techParams = {
            demandResponse: {
              priceUpper: data.demand_response_declared_price_caps || "",
              priceLower: data.demand_response_filing_price_caps || ""
            },
            peakRegulation: {
              priceUpper: data.peaking_response_declared_price_floor || "",
              priceLower: data.peaking_response_filing_price_floor || ""
            },
            frequencyRegulation: {
              priceUpper: data.pm_response_declared_price_floor || "",
              priceLower: data.pm_response_filing_price_floor || ""
            },
            regulation: { priceUpper: "", priceLower: "" },
            energy: { priceUpper: "", priceLower: "" }
          };

          // 映射容量统计数据
          this.capacityStats = {
            userCount: data.user_count || "--",
            resourceCount: data.resource_count || "--",
            totalCapacity: data.adjustable_capacity || "--"
          };
        } else {
          this.$message.error(response.msg || "获取虚拟电厂数据失败");
        }
      } catch (error) {
        console.error("加载虚拟电厂数据失败:", error);
        this.$message.error("加载数据失败");
      }
    },

    // 配置虚拟电厂
    handleConfigVpp() {
      this.configDialogVisible = true;
    },

    handleConfigConfirm(data) {
      console.log("Received data from dialog:", data);
      this.vppInfo = {
        ...this.vppInfo,
        ...data
      };
      this.techParams.demandResponse = data.demandResponse;
      this.techParams.peakRegulation = data.peakRegulation;
      this.techParams.frequencyRegulation = data.frequencyRegulation;

      // 根据场站类型决定vppTypes
      if (data.stationType === "regulation") {
        this.vppInfo.vppTypes = [3]; // 假设3代表调节型
      } else if (data.stationType === "capacity") {
        this.vppInfo.vppTypes = [4]; // 假设4代表能量型/容量型
      } else {
        this.vppInfo.vppTypes = [];
      }

      this.configDialogVisible = false;
    },
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0 && response.data) {
          // 提取省份数据
          this.provinceOptions = response.data.map(province => ({
            code: province.code,
            name: province.name
          }));
        } else {
          console.error("加载地理数据失败:", response.msg);
          this.$message.error(this.$T("加载省份数据失败"));
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error(this.$T("加载省份数据失败"));
      }
    },

    // 根据省份代码获取省份名称
    getProvinceNameByCode(code) {
      const province = this.provinceOptions.find(p => p.code === code);
      return province ? province.name : code;
    },

    // 根据VPP类型获取类型文本
    getVppTypeText(type) {
      const typeMap = {
        1: "聚合型",
        2: "协调型",
        3: "混合型"
      };
      return typeMap[type] || "";
    }
  },
  mounted() {
    this.loadVppData();
    this.loadGeographicalData();
  }
};
</script>

<style scoped lang="scss">
.vpp-info-page {
  height: 100%;
  background: var(--BG1);
}
.vpp-info-container {
  padding: var(--J4);
}
/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--J3);
}

.page-title {
  font-size: var(--H3);
  color: var(--T1);
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: var(--J4);
}

/* 虚拟电厂基本信息 */
.vpp-info-section {
  display: flex;
  gap: var(--J3);
}

.vpp-image-container {
  width: 199px;
  height: 258px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.vpp-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 基本信息模块 */
.vpp-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--J4);
}

/* 技术参数卡片 */
.param-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: var(--Ra);
  border: none;
  background-color: var(--BG4);
}

.card-title {
  font-size: var(--Aa);
  color: var(--T2);
  margin: 0 0 var(--J2) 0;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--J4);
  flex: 1;
}

.param-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--J1);
}

.param-label {
  font-size: var(--Aa);
  color: var(--T3);
}

.param-value {
  color: var(--T1);
}

/* 资源统计区域 */
.stats-section {
  display: flex;
  flex-direction: column;
  gap: var(--J3);
}

.stats-header {
  display: flex;
  align-items: center;
}

.stats-title {
  font-size: var(--H3);
  color: var(--T1);
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: var(--J3);
}

.stats-card {
  flex: 1;
  background: var(--BG4);
  border-radius: var(--Ra);
  padding: var(--J3);
  height: 88px;
  display: flex;
  align-items: center;
  gap: var(--J3);
}

.stats-icon {
  width: 56px;
  height: 56px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--Ra);
}

.icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stats-label {
  font-size: var(--Aa);
  color: var(--T3);
  margin-bottom: var(--J0);
}

.stats-value {
  font-size: var(--H);
  color: var(--T1);
}
</style>
