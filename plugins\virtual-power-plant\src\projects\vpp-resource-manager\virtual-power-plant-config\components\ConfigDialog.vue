<template>
  <el-dialog
    :title="$T('配置虚拟电厂')"
    :visible.sync="dialogVisible"
    width="880px"
    @close="handleClose"
    class="config-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂名称')" prop="vppName">
              <el-input
                v-model="form.vppName"
                :placeholder="$T('请输入内容')"
                class="custom-input"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂所属省份')" prop="province">
              <el-select
                v-model="form.province"
                :placeholder="$T('请选择省份')"
                class="custom-input"
              >
                <el-option
                  v-for="province in provinceOptions"
                  :key="province.code"
                  :label="province.name"
                  :value="province.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂场站类型')" prop="stationType">
              <el-select
                v-model="form.stationType"
                :placeholder="$T('请选择类型')"
                class="custom-input"
              >
                <el-option :label="$T('调节型')" value="regulation"></el-option>
                <el-option :label="$T('容量型')" value="capacity"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂成立日期')" prop="establishDate">
              <el-date-picker
                v-model="form.establishDate"
                type="date"
                :placeholder="$T('请选择日期')"
                class="custom-input"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('运营商编号')" prop="operatorCode">
              <el-input
                v-model="form.operatorCode"
                :placeholder="$T('请输入内容')"
                class="custom-input"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$T('虚拟电厂厂站类型说明')">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.stationTypeDesc"
            class="station-desc-input"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$T('上传图片')">
          <el-upload
            action="#"
            list-type="picture-card"
            :file-list="fileList"
            class="custom-upload"
          ></el-upload>
          <p class="upload-tip">
            {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
          </p>
        </el-form-item>

        <!-- 需求响应 -->
        <h4>{{ $T("需求响应") }}</h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="demandResponse.priceUpper"
            >
              <el-input
                v-model="form.demandResponse.priceUpper"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="demandResponse.priceLower"
            >
              <el-input
                v-model="form.demandResponse.priceLower"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调峰 -->
        <h4>{{ $T("调峰") }}</h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="peakRegulation.priceUpper"
            >
              <el-input
                v-model="form.peakRegulation.priceUpper"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="peakRegulation.priceLower"
            >
              <el-input
                v-model="form.peakRegulation.priceLower"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调频 -->
        <h4>{{ $T("调频") }}</h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="frequencyRegulation.priceUpper"
            >
              <el-input
                v-model="form.frequencyRegulation.priceUpper"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MW") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="frequencyRegulation.priceLower"
            >
              <el-input
                v-model="form.frequencyRegulation.priceLower"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MW") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ $T("确定") }}
      </el-button>
    </span>

    <el-dialog :visible.sync="imagePreviewVisible">
      <img width="100%" :src="imagePreviewUrl" alt="" />
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: "ConfigDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    vppData: {
      type: Object,
      default: () => ({})
    },
    provinceOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        vppName: "",
        province: "",
        stationType: "regulation",
        establishDate: "",
        operatorCode: "",
        stationTypeDesc: "",
        demandResponse: {
          priceUpper: "",
          priceLower: ""
        },
        peakRegulation: {
          priceUpper: "",
          priceLower: ""
        },
        frequencyRegulation: {
          priceUpper: "",
          priceLower: ""
        }
      },
      rules: {
        vppName: [
          { required: true, message: "请输入虚拟电厂名称", trigger: "blur" }
        ],
        province: [
          { required: true, message: "请选择所属省份", trigger: "change" }
        ],
        stationType: [
          { required: true, message: "请选择场站类型", trigger: "change" }
        ],
        establishDate: [
          { required: true, message: "请选择成立日期", trigger: "change" }
        ],
        operatorCode: [
          { required: true, message: "请输入运营商编号", trigger: "blur" }
        ]
      },
      fileList: [],
      imagePreviewVisible: false,
      imagePreviewUrl: ""
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.form = Object.assign({}, this.form, this.vppData);
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit("confirm", this.form);
          this.handleClose();
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.config-dialog {
  :deep(.el-dialog__body) {
    max-height: 600px;
  }

  .dialog-content {
    max-height: 576px;
    overflow-y: auto;
  }
}
h4 {
  margin: 0;
}
.upload-tip {
  color: var(--T4);
  font-size: var(--Ab);
  margin: 0;
}
.custom-input {
  width: 256px;
}

.price-input {
  width: 282px;
}

.custom-upload {
  height: 80px;
  width: 80px;
  :deep(.el-upload--picture-card) {
    width: 100%;
    height: 100%;
  }
}

.station-desc-input {
  :deep(.el-textarea__inner) {
    background-color: var(--BG);
  }
}
</style>
